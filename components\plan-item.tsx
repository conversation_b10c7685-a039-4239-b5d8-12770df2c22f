"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Edit2, Trash2, GripVertical, Save, X } from "lucide-react"

interface Plan {
  id: string
  title: string
  completed: boolean
  priority: "high" | "medium" | "low"
  notes: string
}

interface PlanItemProps {
  plan: Plan
  onUpdate: (plan: Plan) => void
  onDelete: (id: string) => void
}

export function PlanItem({ plan, onUpdate, onDelete }: PlanItemProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editTitle, setEditTitle] = useState(plan.title)
  const [editNotes, setEditNotes] = useState(plan.notes)
  const [editPriority, setEditPriority] = useState(plan.priority)

  const handleSave = () => {
    onUpdate({
      ...plan,
      title: editTitle,
      notes: editNotes,
      priority: editPriority,
    })
    setIsEditing(false)
  }

  const handleCancel = () => {
    setEditTitle(plan.title)
    setEditNotes(plan.notes)
    setEditPriority(plan.priority)
    setIsEditing(false)
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "bg-red-100 text-red-800"
      case "medium":
        return "bg-yellow-100 text-yellow-800"
      case "low":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <Card
      className={`mb-3 transition-all duration-200 ${plan.completed ? "opacity-75 bg-gray-50" : "hover:shadow-md"}`}
    >
      <CardContent className="p-4">
        <div className="flex items-start space-x-3">
          <div className="flex items-center space-x-2 mt-1">
            <GripVertical className="h-4 w-4 text-gray-400 cursor-grab" />
            <Checkbox
              checked={plan.completed}
              onCheckedChange={(checked) => onUpdate({ ...plan, completed: !!checked })}
            />
          </div>

          <div className="flex-1">
            {isEditing ? (
              <div className="space-y-3">
                <Input value={editTitle} onChange={(e) => setEditTitle(e.target.value)} placeholder="计划标题" />
                <Textarea
                  value={editNotes}
                  onChange={(e) => setEditNotes(e.target.value)}
                  placeholder="备注或完成情况"
                  rows={2}
                />
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-600">优先级:</span>
                  <select
                    value={editPriority}
                    onChange={(e) => setEditPriority(e.target.value as "high" | "medium" | "low")}
                    className="text-sm border rounded px-2 py-1"
                  >
                    <option value="high">高</option>
                    <option value="medium">中</option>
                    <option value="low">低</option>
                  </select>
                </div>
              </div>
            ) : (
              <div>
                <div className="flex items-center space-x-2 mb-2">
                  <h3 className={`font-medium ${plan.completed ? "line-through text-gray-500" : "text-gray-900"}`}>
                    {plan.title}
                  </h3>
                  <Badge className={getPriorityColor(plan.priority)}>
                    {plan.priority === "high" ? "高" : plan.priority === "medium" ? "中" : "低"}
                  </Badge>
                </div>
                {plan.notes && <p className="text-sm text-gray-600 mt-1">{plan.notes}</p>}
              </div>
            )}
          </div>

          <div className="flex items-center space-x-1">
            {isEditing ? (
              <>
                <Button size="sm" variant="ghost" onClick={handleSave}>
                  <Save className="h-4 w-4" />
                </Button>
                <Button size="sm" variant="ghost" onClick={handleCancel}>
                  <X className="h-4 w-4" />
                </Button>
              </>
            ) : (
              <>
                <Button size="sm" variant="ghost" onClick={() => setIsEditing(true)}>
                  <Edit2 className="h-4 w-4" />
                </Button>
                <Button size="sm" variant="ghost" onClick={() => onDelete(plan.id)}>
                  <Trash2 className="h-4 w-4" />
                </Button>
              </>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
