"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Calendar, Home, Settings, User, BarChart3, <PERSON><PERSON><PERSON>, Apple } from "lucide-react"
import Link from "next/link"
import { usePathname } from "next/navigation"

export function Navigation() {
  const pathname = usePathname()

  const isActive = (path: string) => {
    return pathname === path
  }

  return (
    <nav className="bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 shadow-lg">
      <div className="max-w-7xl mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-8">
            <Link href="/" className="flex items-center space-x-3">
              <div className="relative">
                <Calendar className="h-8 w-8 text-white" />
                <Sparkles className="h-4 w-4 text-yellow-300 absolute -top-1 -right-1" />
              </div>
              <div>
                <span className="text-xl font-bold text-white">每日计划</span>
                <p className="text-xs text-indigo-100">让每一天都充满意义</p>
              </div>
            </Link>

            <div className="hidden md:flex items-center space-x-1">
              <Link href="/">
                <Button
                  variant="ghost"
                  className={`text-white hover:bg-white/20 transition-all duration-200 ${
                    isActive("/") ? "bg-white/20" : ""
                  }`}
                >
                  <Home className="h-4 w-4 mr-2" />
                  <span>首页</span>
                </Button>
              </Link>

              <Link href="/">
                <Button
                  variant="ghost"
                  className={`text-white hover:bg-white/20 transition-all duration-200 ${
                    isActive("/") ? "bg-white/20" : ""
                  }`}
                >
                  <Calendar className="h-4 w-4 mr-2" />
                  <span>计划</span>
                </Button>
              </Link>

              <Link href="/fruit-plan">
                <Button
                  variant="ghost"
                  className={`text-white hover:bg-white/20 transition-all duration-200 ${
                    isActive("/fruit-plan") ? "bg-white/20" : ""
                  }`}
                >
                  <Apple className="h-4 w-4 mr-2" />
                  <span>水果计划</span>
                </Button>
              </Link>

              <Button variant="ghost" className="text-white hover:bg-white/20 transition-all duration-200">
                <BarChart3 className="h-4 w-4 mr-2" />
                <span>统计</span>
              </Button>

              <Button variant="ghost" className="text-white hover:bg-white/20 transition-all duration-200">
                <Settings className="h-4 w-4 mr-2" />
                <span>设置</span>
              </Button>
            </div>
          </div>

          <Button variant="ghost" className="text-white hover:bg-white/20 transition-all duration-200">
            <User className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">个人中心</span>
          </Button>
        </div>
      </div>
    </nav>
  )
}
