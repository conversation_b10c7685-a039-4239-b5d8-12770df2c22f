"use client"

import { useState, useEffect } from "react"
import { Navigation } from "../../components/navigation"
import { SimpleFruitCard } from "../../components/simple-fruit-card"
import { SimpleFruitStats } from "../../components/simple-fruit-stats"
import { SimpleRecommendations } from "../../components/simple-recommendations"
import { EditableHealthTips } from "../../components/editable-health-tips"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Apple, CalendarIcon, Target, ChevronLeft, ChevronRight } from "lucide-react"
import { format, startOfWeek, addDays, addWeeks, subWeeks } from "date-fns"
import { zhCN } from "date-fns/locale"

interface DayFruit {
  id: string
  name: string
  completed: boolean
  comment: string
  rating: number
}

interface Recommendation {
  id: string
  name: string
  reason: string
}

interface HealthTip {
  id: string
  title: string
  content: string
  color: string
}

const defaultRecommendations: Recommendation[] = [
  { id: "1", name: "苹果", reason: "四季皆宜，营养均衡" },
  { id: "2", name: "香蕉", reason: "补充能量，富含钾元素" },
  { id: "3", name: "橙子", reason: "维生素C丰富，增强免疫力" },
]

const defaultHealthTips: HealthTip[] = [
  {
    id: "1",
    title: "🍎 每日建议",
    content: "成人每天应摄入200-400克水果，相当于1-2个中等大小的苹果。",
    color: "blue",
  },
  {
    id: "2",
    title: "🌈 多样化",
    content: "不同颜色的水果含有不同的营养素，建议每天摄入多种颜色的水果。",
    color: "green",
  },
  {
    id: "3",
    title: "⏰ 最佳时间",
    content: "上午和下午是吃水果的最佳时间，有助于营养吸收。",
    color: "orange",
  },
]

export default function FruitPlanPage() {
  const [selectedWeek, setSelectedWeek] = useState<Date>(new Date())
  const [weekData, setWeekData] = useState<Record<string, DayFruit[]>>({})
  const [weeklyGoal, setWeeklyGoal] = useState(21)
  const [recommendations, setRecommendations] = useState<Recommendation[]>(defaultRecommendations)
  const [healthTips, setHealthTips] = useState<HealthTip[]>(defaultHealthTips)

  // 获取本周日期
  const getWeekDays = (date: Date) => {
    const start = startOfWeek(date, { weekStartsOn: 1 })
    return Array.from({ length: 7 }, (_, i) => addDays(start, i))
  }

  const weekDays = getWeekDays(selectedWeek)

  // 数据持久化
  useEffect(() => {
    const savedData = localStorage.getItem("simpleFruitPlanData")
    const savedRecommendations = localStorage.getItem("simpleFruitRecommendations")
    const savedHealthTips = localStorage.getItem("simpleFruitHealthTips")

    if (savedData) setWeekData(JSON.parse(savedData))
    if (savedRecommendations) setRecommendations(JSON.parse(savedRecommendations))
    if (savedHealthTips) setHealthTips(JSON.parse(savedHealthTips))
  }, [])

  useEffect(() => {
    localStorage.setItem("simpleFruitPlanData", JSON.stringify(weekData))
  }, [weekData])

  useEffect(() => {
    localStorage.setItem("simpleFruitRecommendations", JSON.stringify(recommendations))
  }, [recommendations])

  useEffect(() => {
    localStorage.setItem("simpleFruitHealthTips", JSON.stringify(healthTips))
  }, [healthTips])

  const addFruit = (day: string, name: string) => {
    const newFruit: DayFruit = {
      id: Date.now().toString(),
      name,
      completed: false,
      comment: "",
      rating: 0,
    }
    setWeekData((prev) => ({
      ...prev,
      [day]: [...(prev[day] || []), newFruit],
    }))
  }

  const removeFruit = (day: string, fruitId: string) => {
    setWeekData((prev) => ({
      ...prev,
      [day]: (prev[day] || []).filter((f) => f.id !== fruitId),
    }))
  }

  const toggleComplete = (day: string, fruitId: string) => {
    setWeekData((prev) => ({
      ...prev,
      [day]: (prev[day] || []).map((f) => (f.id === fruitId ? { ...f, completed: !f.completed } : f)),
    }))
  }

  const updateComment = (day: string, fruitId: string, comment: string) => {
    setWeekData((prev) => ({
      ...prev,
      [day]: (prev[day] || []).map((f) => (f.id === fruitId ? { ...f, comment } : f)),
    }))
  }

  const updateRating = (day: string, fruitId: string, rating: number) => {
    setWeekData((prev) => ({
      ...prev,
      [day]: (prev[day] || []).map((f) => (f.id === fruitId ? { ...f, rating } : f)),
    }))
  }

  const addRecommendation = (name: string) => {
    const today = format(new Date(), "yyyy-MM-dd")
    addFruit(today, name)
  }

  // 计算统计数据
  const totalPlanned = Object.values(weekData).reduce((sum, day) => sum + day.length, 0)
  const totalCompleted = Object.values(weekData).reduce((sum, day) => sum + day.filter((f) => f.completed).length, 0)
  const goalProgress = Math.round((totalCompleted / weeklyGoal) * 100)

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-red-50 to-pink-50">
      <Navigation />

      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* 页面头部 */}
        <div className="mb-8 flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent mb-2 flex items-center">
              <Apple className="h-8 w-8 mr-3 text-orange-500" />
              水果计划
            </h1>
            <p className="text-gray-600">健康生活，从每天一个水果开始 🍎</p>
          </div>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSelectedWeek(subWeeks(selectedWeek, 1))}
                className="bg-white"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="bg-white shadow-md hover:shadow-lg transition-all duration-200">
                    <CalendarIcon className="mr-2 h-4 w-4 text-orange-500" />
                    {format(selectedWeek, "yyyy年MM月dd日 当周", { locale: zhCN })}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={selectedWeek}
                    onSelect={(date) => date && setSelectedWeek(date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSelectedWeek(addWeeks(selectedWeek, 1))}
                className="bg-white"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* 周目标进度 */}
        <Card className="mb-8 bg-gradient-to-r from-orange-100 to-red-100 border-orange-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <Target className="h-6 w-6 text-orange-600" />
                <div>
                  <h3 className="text-lg font-bold text-orange-800">本周目标进度</h3>
                  <p className="text-sm text-orange-600">目标：每周{weeklyGoal}个水果</p>
                </div>
              </div>
              <div className="text-right">
                <div className="text-3xl font-bold text-orange-600">
                  {totalCompleted}/{weeklyGoal}
                </div>
                <Badge
                  className={`${goalProgress >= 100 ? "bg-green-500" : goalProgress >= 70 ? "bg-yellow-500" : "bg-orange-500"} text-white`}
                >
                  {goalProgress}%
                </Badge>
              </div>
            </div>
            <div className="w-full bg-orange-200 rounded-full h-3">
              <div
                className="bg-gradient-to-r from-orange-400 to-red-400 h-3 rounded-full transition-all duration-500"
                style={{ width: `${Math.min(goalProgress, 100)}%` }}
              ></div>
            </div>
          </CardContent>
        </Card>

        {/* 每日水果计划 */}
        <div className="mb-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {weekDays.map((day) => {
              const dayKey = format(day, "yyyy-MM-dd")
              const dayName = format(day, "EEEE", { locale: zhCN })

              return (
                <SimpleFruitCard
                  key={dayKey}
                  day={dayKey}
                  dayName={dayName}
                  fruits={weekData[dayKey] || []}
                  onAddFruit={addFruit}
                  onRemoveFruit={removeFruit}
                  onToggleComplete={toggleComplete}
                  onUpdateComment={updateComment}
                  onUpdateRating={updateRating}
                />
              )
            })}
          </div>
        </div>

        {/* 统计和管理区域 */}
        <div className="space-y-8">
          <SimpleFruitStats weekData={weekData} />

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <SimpleRecommendations
              recommendations={recommendations}
              onUpdateRecommendations={setRecommendations}
              onAddRecommendation={addRecommendation}
            />
            <EditableHealthTips tips={healthTips} onUpdateTips={setHealthTips} />
          </div>
        </div>
      </div>
    </div>
  )
}
