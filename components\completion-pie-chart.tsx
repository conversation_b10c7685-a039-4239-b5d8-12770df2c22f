"use client"

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Responsive<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "recharts"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { TrendingUp } from "lucide-react"

interface CompletionPieChartProps {
  completed: number
  total: number
}

export function CompletionPieChart({ completed, total }: CompletionPieChartProps) {
  const incomplete = total - completed
  const completionRate = total > 0 ? Math.round((completed / total) * 100) : 0

  const data = [
    { name: "已完成", value: completed, color: "#10b981" },
    { name: "未完成", value: incomplete, color: "#e5e7eb" },
  ]

  const COLORS = ["#10b981", "#e5e7eb"]

  return (
    <Card className="bg-gradient-to-br from-emerald-50 to-teal-50 border-emerald-200 shadow-lg hover:shadow-xl transition-all duration-300">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg flex items-center text-emerald-700">
          <TrendingUp className="h-5 w-5 mr-2" />
          完成率
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="relative">
          <ResponsiveContainer width="100%" height={120}>
            <PieChart>
              <Pie data={data} cx="50%" cy="50%" innerRadius={30} outerRadius={50} paddingAngle={2} dataKey="value">
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip formatter={(value: number) => [`${value}项`, ""]} />
            </PieChart>
          </ResponsiveContainer>
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <div className="text-3xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                {completionRate}%
              </div>
              <div className="text-xs text-emerald-600 font-medium">完成率</div>
            </div>
          </div>
        </div>
        <div className="flex justify-center space-x-4 mt-3 text-sm">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-emerald-500 rounded-full shadow-sm"></div>
            <span className="text-emerald-700 font-medium">已完成 {completed}</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-gray-300 rounded-full shadow-sm"></div>
            <span className="text-gray-600 font-medium">未完成 {incomplete}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
