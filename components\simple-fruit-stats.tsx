"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { ResponsiveContainer, Tooltip, BarChart, Bar, XAxis, YAxis, CartesianGrid } from "recharts"
import { TrendingUp, Target } from "lucide-react"

interface DayFruit {
  id: string
  name: string
  completed: boolean
  comment: string
  rating: number
}

interface SimpleFruitStatsProps {
  weekData: Record<string, DayFruit[]>
}

export function SimpleFruitStats({ weekData }: SimpleFruitStatsProps) {
  // 计算统计数据
  const totalPlanned = Object.values(weekData).reduce((sum, day) => sum + day.length, 0)
  const totalCompleted = Object.values(weekData).reduce((sum, day) => sum + day.filter((f) => f.completed).length, 0)
  const completionRate = totalPlanned > 0 ? Math.round((totalCompleted / totalPlanned) * 100) : 0

  // 水果类型统计
  const fruitTypeStats = Object.values(weekData)
    .flat()
    .filter((f) => f.completed)
    .reduce(
      (acc, fruit) => {
        const existing = acc.find((item) => item.name === fruit.name)
        if (existing) {
          existing.count += 1
        } else {
          acc.push({ name: fruit.name, count: 1 })
        }
        return acc
      },
      [] as { name: string; count: number }[],
    )
    .sort((a, b) => b.count - a.count)
    .slice(0, 8) // 只显示前8个

  // 每日完成情况
  const dailyStats = Object.entries(weekData).map(([day, fruits]) => ({
    day: day.split("-")[2] + "日",
    completed: fruits.filter((f) => f.completed).length,
    total: fruits.length,
    rate: fruits.length > 0 ? Math.round((fruits.filter((f) => f.completed).length / fruits.length) * 100) : 0,
  }))

  const COLORS = ["#ff6b6b", "#4ecdc4", "#45b7d1", "#96ceb4", "#feca57", "#ff9ff3", "#54a0ff", "#5f27cd"]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {/* 总体统计 */}
      <Card className="bg-gradient-to-br from-green-50 to-emerald-50 border-green-200">
        <CardHeader>
          <CardTitle className="text-lg flex items-center">
            <Target className="h-5 w-5 mr-2 text-green-600" />
            完成统计
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center">
            <div className="text-4xl font-bold text-green-600 mb-2">{completionRate}%</div>
            <div className="text-sm text-green-700 mb-4">本周完成率</div>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">{totalCompleted}</div>
                <div className="text-gray-600">已完成</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{totalPlanned}</div>
                <div className="text-gray-600">总计划</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 每日完成情况 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center text-lg">
            <TrendingUp className="h-5 w-5 mr-2 text-blue-500" />
            每日完成情况
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-48">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={dailyStats}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="day" tick={{ fontSize: 12 }} />
                <YAxis tick={{ fontSize: 12 }} />
                <Tooltip
                  formatter={(value: number, name: string) => [
                    name === "completed" ? `${value}个` : `${value}%`,
                    name === "completed" ? "已完成" : "完成率",
                  ]}
                />
                <Bar dataKey="completed" fill="#4ecdc4" radius={[4, 4, 0, 0]} />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      {/* 水果类型分布 */}
      {fruitTypeStats.length > 0 && (
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <TrendingUp className="h-5 w-5 mr-2 text-purple-500" />
              热门水果排行
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {fruitTypeStats.map((fruit, index) => (
                <div key={fruit.name} className="text-center p-3 bg-gray-50 rounded-lg">
                  <div className="text-2xl font-bold text-gray-800 mb-1">{fruit.count}</div>
                  <div className="text-sm text-gray-600">{fruit.name}</div>
                  <div className="text-xs text-gray-500">第{index + 1}名</div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
