"use client"

import { Bad<PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import {
  Code2,
  Palette,
  Smartphone,
  Zap,
  Database,
  Globe,
  Sparkles,
  Brain,
  Rocket,
  Shield,
  Users,
  ChevronRight,
  Star,
  CheckCircle,
} from "lucide-react"
import { useState, useEffect } from "react"

export default function Component() {
  const [animatedProgress, setAnimatedProgress] = useState(0)

  useEffect(() => {
    const timer = setTimeout(() => setAnimatedProgress(95), 500)
    return () => clearTimeout(timer)
  }, [])

  const capabilities = [
    {
      icon: Code2,
      title: "代码生成",
      description: "生成高质量的React、Next.js、TypeScript代码",
      progress: 98,
      color: "bg-blue-500",
    },
    {
      icon: Palette,
      title: "UI/UX设计",
      description: "创建美观、现代的用户界面设计",
      progress: 95,
      color: "bg-purple-500",
    },
    {
      icon: Smartphone,
      title: "响应式设计",
      description: "适配所有设备尺寸的响应式布局",
      progress: 97,
      color: "bg-green-500",
    },
    {
      icon: Database,
      title: "数据库集成",
      description: "Supabase、Neon等数据库无缝集成",
      progress: 92,
      color: "bg-orange-500",
    },
    {
      icon: Brain,
      title: "AI集成",
      description: "集成各种AI服务和模型",
      progress: 94,
      color: "bg-pink-500",
    },
    {
      icon: Zap,
      title: "性能优化",
      description: "优化代码性能和用户体验",
      progress: 96,
      color: "bg-yellow-500",
    },
  ]

  const techStack = [
    "React",
    "Next.js",
    "TypeScript",
    "Tailwind CSS",
    "shadcn/ui",
    "Supabase",
    "Vercel",
    "Node.js",
    "Python",
    "SQL",
    "AI SDK",
  ]

  const features = [
    {
      icon: Rocket,
      title: "快速开发",
      description: "几秒钟内生成完整的应用程序",
    },
    {
      icon: Shield,
      title: "最佳实践",
      description: "遵循行业标准和安全最佳实践",
    },
    {
      icon: Users,
      title: "用户友好",
      description: "创建直观易用的用户界面",
    },
    {
      icon: Globe,
      title: "现代技术",
      description: "使用最新的Web技术栈",
    },
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 text-white">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative container mx-auto px-4 py-24 text-center">
          <div className="inline-flex items-center gap-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 mb-6">
            <Sparkles className="h-4 w-4" />
            <span className="text-sm font-medium">AI-Powered Development</span>
          </div>
          <h1 className="text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent">
            我是 v0
          </h1>
          <p className="text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto">
            Vercel的AI驱动助手，专门为你创建现代化的Web应用程序和用户界面
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-white text-blue-600 hover:bg-blue-50">
              开始体验
              <ChevronRight className="ml-2 h-4 w-4" />
            </Button>
            <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10 bg-transparent">
              查看案例
            </Button>
          </div>
        </div>
      </section>

      {/* Capabilities Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold mb-4">核心能力展示</h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              从代码生成到UI设计，我具备全栈开发的各项技能
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {capabilities.map((capability, index) => {
              const Icon = capability.icon
              return (
                <Card key={index} className="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                  <CardHeader>
                    <div className="flex items-center gap-3">
                      <div className={`p-2 rounded-lg ${capability.color} text-white`}>
                        <Icon className="h-6 w-6" />
                      </div>
                      <div>
                        <CardTitle className="text-lg">{capability.title}</CardTitle>
                        <CardDescription>{capability.description}</CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>熟练度</span>
                        <span className="font-semibold">{capability.progress}%</span>
                      </div>
                      <Progress value={capability.progress} className="h-2" />
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      {/* Tech Stack Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold mb-4">技术栈掌握</h2>
            <p className="text-xl text-muted-foreground">精通现代Web开发的各种技术和工具</p>
          </div>

          <div className="flex flex-wrap justify-center gap-3">
            {techStack.map((tech, index) => (
              <Badge
                key={index}
                variant="secondary"
                className="px-4 py-2 text-sm font-medium hover:bg-primary hover:text-primary-foreground transition-colors cursor-pointer"
              >
                {tech}
              </Badge>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold mb-4">特色功能</h2>
            <p className="text-xl text-muted-foreground">让开发变得更简单、更高效的核心特性</p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => {
              const Icon = feature.icon
              return (
                <div key={index} className="text-center group">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full text-white mb-4 group-hover:scale-110 transition-transform">
                    <Icon className="h-8 w-8" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
                  <p className="text-muted-foreground">{feature.description}</p>
                </div>
              )
            })}
          </div>
        </div>
      </section>

      {/* Interactive Demo Section */}
      <section className="py-20 bg-gradient-to-r from-slate-900 to-slate-800 text-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold mb-4">互动演示</h2>
            <p className="text-xl text-blue-100">体验我的各种能力和功能</p>
          </div>

          <Tabs defaultValue="code" className="max-w-4xl mx-auto">
            <TabsList className="grid w-full grid-cols-4 bg-slate-800">
              <TabsTrigger value="code">代码生成</TabsTrigger>
              <TabsTrigger value="ui">UI设计</TabsTrigger>
              <TabsTrigger value="data">数据处理</TabsTrigger>
              <TabsTrigger value="ai">AI集成</TabsTrigger>
            </TabsList>

            <TabsContent value="code" className="mt-8">
              <Card className="bg-slate-800 border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white">React组件生成</CardTitle>
                  <CardDescription className="text-slate-300">快速生成高质量的React组件代码</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="bg-slate-900 rounded-lg p-4 font-mono text-sm text-green-400">
                    <div className="text-blue-400">{"// 生成的React组件示例"}</div>
                    <div className="text-purple-400">
                      {"export default function"} <span className="text-yellow-400">Button</span>() {"{"}
                    </div>
                    <div className="ml-4 text-white">{"return ("}</div>
                    <div className="ml-8 text-red-400">{'<button className="px-4 py-2 bg-blue-500">'}</div>
                    <div className="ml-12 text-white">{"点击我"}</div>
                    <div className="ml-8 text-red-400">{"</button>"}</div>
                    <div className="ml-4 text-white">{")"}</div>
                    <div className="text-purple-400">{"}"}</div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="ui" className="mt-8">
              <Card className="bg-slate-800 border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white">界面设计能力</CardTitle>
                  <CardDescription className="text-slate-300">创建美观现代的用户界面</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg p-4 text-center">
                      <Star className="h-8 w-8 mx-auto mb-2" />
                      <p className="font-semibold">现代设计</p>
                    </div>
                    <div className="bg-gradient-to-r from-green-500 to-blue-500 rounded-lg p-4 text-center">
                      <CheckCircle className="h-8 w-8 mx-auto mb-2" />
                      <p className="font-semibold">响应式布局</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="data" className="mt-8">
              <Card className="bg-slate-800 border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white">数据库集成</CardTitle>
                  <CardDescription className="text-slate-300">无缝集成各种数据库服务</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                      <span>Supabase 集成</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                      <span>Neon 数据库</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                      <span>Upstash Redis</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="ai" className="mt-8">
              <Card className="bg-slate-800 border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white">AI服务集成</CardTitle>
                  <CardDescription className="text-slate-300">集成多种AI模型和服务</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>• xAI (Grok)</div>
                    <div>• Groq</div>
                    <div>• Fal AI</div>
                    <div>• DeepInfra</div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl font-bold mb-4">准备开始了吗？</h2>
          <p className="text-xl mb-8 text-blue-100 max-w-2xl mx-auto">让我帮你构建下一个令人惊叹的Web应用程序</p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-white text-blue-600 hover:bg-blue-50">
              立即开始
              <Rocket className="ml-2 h-4 w-4" />
            </Button>
            <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10 bg-transparent">
              了解更多
            </Button>
          </div>
        </div>
      </section>
    </div>
  )
}
