"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardH<PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Plus, Edit2, Trash2, Save, X } from "lucide-react"

interface Fruit {
  id: string
  name: string
  emoji: string
  color: string
  nutrition: {
    calories: number
    vitamin: string
    fiber: number
  }
  benefits: string[]
}

interface CustomFruitManagerProps {
  fruits: Fruit[]
  onAddFruit: (fruit: Omit<Fruit, "id">) => void
  onUpdateFruit: (fruit: Fruit) => void
  onDeleteFruit: (id: string) => void
}

export function CustomFruitManager({ fruits, onAddFruit, onUpdateFruit, onDeleteFruit }: CustomFruitManagerProps) {
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingId, setEditingId] = useState<string | null>(null)
  const [formData, setFormData] = useState({
    name: "",
    emoji: "",
    calories: 0,
    vitamin: "",
    fiber: 0,
  })

  const handleSubmit = () => {
    if (formData.name && formData.emoji) {
      if (editingId) {
        const existingFruit = fruits.find((f) => f.id === editingId)
        if (existingFruit) {
          onUpdateFruit({
            ...existingFruit,
            name: formData.name,
            emoji: formData.emoji,
            nutrition: {
              calories: formData.calories,
              vitamin: formData.vitamin,
              fiber: formData.fiber,
            },
          })
        }
        setEditingId(null)
      } else {
        onAddFruit({
          name: formData.name,
          emoji: formData.emoji,
          color: "#ff6b6b",
          nutrition: {
            calories: formData.calories,
            vitamin: formData.vitamin,
            fiber: formData.fiber,
          },
          benefits: [],
        })
      }
      setFormData({ name: "", emoji: "", calories: 0, vitamin: "", fiber: 0 })
      setShowAddForm(false)
    }
  }

  const handleEdit = (fruit: Fruit) => {
    setEditingId(fruit.id)
    setFormData({
      name: fruit.name,
      emoji: fruit.emoji,
      calories: fruit.nutrition.calories,
      vitamin: fruit.nutrition.vitamin,
      fiber: fruit.nutrition.fiber,
    })
    setShowAddForm(true)
  }

  const handleCancel = () => {
    setShowAddForm(false)
    setEditingId(null)
    setFormData({ name: "", emoji: "", calories: 0, vitamin: "", fiber: 0 })
  }

  return (
    <Card className="bg-gradient-to-br from-green-50 to-blue-50 border-green-200">
      <CardHeader>
        <CardTitle className="flex items-center justify-between text-lg">
          <span className="flex items-center">
            <Plus className="h-5 w-5 mr-2 text-green-500" />
            自定义水果
          </span>
          {!showAddForm && (
            <Button size="sm" onClick={() => setShowAddForm(true)} className="bg-green-500 hover:bg-green-600">
              <Plus className="h-3 w-3 mr-1" />
              添加
            </Button>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {showAddForm && (
          <div className="p-4 bg-white rounded-lg border border-green-200 space-y-3">
            <div className="grid grid-cols-2 gap-3">
              <div>
                <Label htmlFor="name" className="text-sm font-medium">
                  水果名称
                </Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="例如：苹果"
                  className="text-sm"
                />
              </div>
              <div>
                <Label htmlFor="emoji" className="text-sm font-medium">
                  表情符号
                </Label>
                <Input
                  id="emoji"
                  value={formData.emoji}
                  onChange={(e) => setFormData({ ...formData, emoji: e.target.value })}
                  placeholder="🍎"
                  className="text-sm"
                />
              </div>
            </div>
            <div className="grid grid-cols-3 gap-3">
              <div>
                <Label htmlFor="calories" className="text-sm font-medium">
                  卡路里
                </Label>
                <Input
                  id="calories"
                  type="number"
                  value={formData.calories}
                  onChange={(e) => setFormData({ ...formData, calories: Number(e.target.value) })}
                  placeholder="52"
                  className="text-sm"
                />
              </div>
              <div>
                <Label htmlFor="vitamin" className="text-sm font-medium">
                  主要维生素
                </Label>
                <Input
                  id="vitamin"
                  value={formData.vitamin}
                  onChange={(e) => setFormData({ ...formData, vitamin: e.target.value })}
                  placeholder="维生素C"
                  className="text-sm"
                />
              </div>
              <div>
                <Label htmlFor="fiber" className="text-sm font-medium">
                  纤维(g)
                </Label>
                <Input
                  id="fiber"
                  type="number"
                  step="0.1"
                  value={formData.fiber}
                  onChange={(e) => setFormData({ ...formData, fiber: Number(e.target.value) })}
                  placeholder="2.4"
                  className="text-sm"
                />
              </div>
            </div>
            <div className="flex space-x-2">
              <Button size="sm" onClick={handleSubmit} className="bg-green-500 hover:bg-green-600">
                <Save className="h-3 w-3 mr-1" />
                {editingId ? "更新" : "添加"}
              </Button>
              <Button size="sm" variant="outline" onClick={handleCancel}>
                <X className="h-3 w-3 mr-1" />
                取消
              </Button>
            </div>
          </div>
        )}

        {/* 自定义水果列表 */}
        <div className="space-y-2 max-h-48 overflow-y-auto">
          {fruits
            .filter(
              (fruit) =>
                !["apple", "banana", "orange", "grape", "strawberry", "kiwi", "mango", "watermelon"].includes(fruit.id),
            )
            .map((fruit) => (
              <div key={fruit.id} className="flex items-center justify-between p-2 bg-white rounded border">
                <div className="flex items-center space-x-2">
                  <span className="text-lg">{fruit.emoji}</span>
                  <span className="font-medium text-sm">{fruit.name}</span>
                  <span className="text-xs text-gray-500">{fruit.nutrition.calories}卡</span>
                </div>
                <div className="flex space-x-1">
                  <Button size="sm" variant="ghost" onClick={() => handleEdit(fruit)} className="p-1">
                    <Edit2 className="h-3 w-3" />
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => onDeleteFruit(fruit.id)}
                    className="p-1 text-red-600"
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            ))}
        </div>
      </CardContent>
    </Card>
  )
}
