"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Leaf, Edit2, Save, Plus, Trash2 } from "lucide-react"

interface Recommendation {
  id: string
  name: string
  reason: string
}

interface SimpleRecommendationsProps {
  recommendations: Recommendation[]
  onUpdateRecommendations: (recommendations: Recommendation[]) => void
  onAddRecommendation: (name: string) => void
}

export function SimpleRecommendations({
  recommendations,
  onUpdateRecommendations,
  onAddRecommendation,
}: SimpleRecommendationsProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editingRecommendations, setEditingRecommendations] = useState<Recommendation[]>(recommendations)
  const [showAddForm, setShowAddForm] = useState(false)
  const [newRecommendation, setNewR<PERSON>ommendation] = useState({ name: "", reason: "" })

  const handleSave = () => {
    onUpdateRecommendations(editingRecommendations)
    setIsEditing(false)
  }

  const handleCancel = () => {
    setEditingRecommendations(recommendations)
    setIsEditing(false)
    setShowAddForm(false)
  }

  const handleUpdateRecommendation = (index: number, field: keyof Recommendation, value: string) => {
    const updated = [...editingRecommendations]
    updated[index] = { ...updated[index], [field]: value }
    setEditingRecommendations(updated)
  }

  const handleAddNew = () => {
    if (newRecommendation.name) {
      const newRec: Recommendation = {
        id: Date.now().toString(),
        ...newRecommendation,
      }
      setEditingRecommendations([...editingRecommendations, newRec])
      setNewRecommendation({ name: "", reason: "" })
      setShowAddForm(false)
    }
  }

  const handleDelete = (index: number) => {
    const updated = editingRecommendations.filter((_, i) => i !== index)
    setEditingRecommendations(updated)
  }

  return (
    <Card className="bg-gradient-to-br from-green-50 to-blue-50 border-green-200">
      <CardHeader>
        <CardTitle className="flex items-center justify-between text-lg">
          <span className="flex items-center">
            <Leaf className="h-5 w-5 mr-2 text-green-500" />
            推荐清单
          </span>
          <Button
            size="sm"
            variant="outline"
            onClick={() => setIsEditing(!isEditing)}
            className="border-green-300 text-green-600 hover:bg-green-50"
          >
            <Edit2 className="h-3 w-3 mr-1" />
            {isEditing ? "取消" : "编辑"}
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {isEditing ? (
          <div className="space-y-4">
            {editingRecommendations.map((rec, index) => (
              <div key={rec.id} className="p-3 bg-white rounded-lg border border-green-100 space-y-2">
                <div className="flex items-center space-x-2">
                  <Input
                    value={rec.name}
                    onChange={(e) => handleUpdateRecommendation(index, "name", e.target.value)}
                    className="flex-1"
                    placeholder="水果名称"
                  />
                  <Button size="sm" variant="ghost" onClick={() => handleDelete(index)} className="text-red-600">
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
                <Textarea
                  value={rec.reason}
                  onChange={(e) => handleUpdateRecommendation(index, "reason", e.target.value)}
                  placeholder="推荐理由"
                  rows={2}
                  className="text-sm"
                />
              </div>
            ))}

            {showAddForm && (
              <div className="p-3 bg-blue-50 rounded-lg border border-blue-200 space-y-2">
                <Input
                  value={newRecommendation.name}
                  onChange={(e) => setNewRecommendation({ ...newRecommendation, name: e.target.value })}
                  placeholder="水果名称"
                />
                <Textarea
                  value={newRecommendation.reason}
                  onChange={(e) => setNewRecommendation({ ...newRecommendation, reason: e.target.value })}
                  placeholder="推荐理由"
                  rows={2}
                  className="text-sm"
                />
                <div className="flex space-x-2">
                  <Button size="sm" onClick={handleAddNew}>
                    添加
                  </Button>
                  <Button size="sm" variant="outline" onClick={() => setShowAddForm(false)}>
                    取消
                  </Button>
                </div>
              </div>
            )}

            <div className="flex space-x-2">
              <Button size="sm" onClick={handleSave} className="bg-green-500 hover:bg-green-600">
                <Save className="h-3 w-3 mr-1" />
                保存更改
              </Button>
              <Button size="sm" variant="outline" onClick={handleCancel}>
                取消
              </Button>
              {!showAddForm && (
                <Button size="sm" variant="outline" onClick={() => setShowAddForm(true)}>
                  <Plus className="h-3 w-3 mr-1" />
                  添加推荐
                </Button>
              )}
            </div>
          </div>
        ) : (
          <div className="space-y-3">
            {recommendations.map((rec) => (
              <div
                key={rec.id}
                className="flex items-center justify-between p-3 bg-white rounded-lg border border-green-100"
              >
                <div>
                  <div className="font-medium text-gray-800">{rec.name}</div>
                  <div className="text-sm text-gray-600">{rec.reason}</div>
                </div>
                <Button
                  size="sm"
                  onClick={() => onAddRecommendation(rec.name)}
                  className="bg-gradient-to-r from-green-400 to-blue-400 hover:from-green-500 hover:to-blue-500 text-white"
                >
                  添加
                </Button>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
