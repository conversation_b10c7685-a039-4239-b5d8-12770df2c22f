"use client"

import { useState, useEffect } from "react"
import { Navigation } from "../components/navigation"
import { DraggablePlanItem } from "../components/draggable-plan-item"
import { ImprovementItem } from "../components/improvement-item"
import { CompletionPieChart } from "../components/completion-pie-chart"
import { StarRating } from "../components/star-rating"
import { DailyReflection } from "../components/daily-reflection"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts"
import { Plus, Target, CalendarIcon, Lightbulb, Tren<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react"
import { format } from "date-fns"
import { zhCN } from "date-fns/locale"

interface Plan {
  id: string
  title: string
  completed: boolean
  priority: "high" | "medium" | "low"
  category: "study" | "work" | "life" | "other"
  notes: string
}

interface Improvement {
  id: string
  title: string
  completed: boolean
}

interface Reflection {
  id: string
  date: string
  content: string
  mood: "happy" | "neutral" | "sad"
  images: string[]
}

export default function DailyPlannerPage() {
  const [selectedDate, setSelectedDate] = useState<Date>(new Date())
  const [plans, setPlans] = useState<Plan[]>([])
  const [improvements, setImprovements] = useState<Improvement[]>([])
  const [newPlanTitle, setNewPlanTitle] = useState("")
  const [newPlanCategory, setNewPlanCategory] = useState<"study" | "work" | "life" | "other">("study")
  const [newImprovementTitle, setNewImprovementTitle] = useState("")
  const [dailyRating, setDailyRating] = useState(0)
  const [reflections, setReflections] = useState<Reflection[]>([])
  const [progressData] = useState([
    { date: "2024-01-15", completionRate: 80, score: 4 },
    { date: "2024-01-16", completionRate: 65, score: 3 },
    { date: "2024-01-17", completionRate: 90, score: 5 },
    { date: "2024-01-18", completionRate: 75, score: 4 },
    { date: "2024-01-19", completionRate: 85, score: 4 },
    { date: "2024-01-20", completionRate: 95, score: 5 },
    { date: "2024-01-21", completionRate: 70, score: 3 },
  ])

  // 数据持久化
  useEffect(() => {
    const savedPlans = localStorage.getItem("dailyPlans")
    const savedImprovements = localStorage.getItem("improvements")
    const savedReflections = localStorage.getItem("dailyReflections")
    const savedRating = localStorage.getItem("dailyRating")

    if (savedPlans) setPlans(JSON.parse(savedPlans))
    if (savedImprovements) setImprovements(JSON.parse(savedImprovements))
    if (savedReflections) setReflections(JSON.parse(savedReflections))
    if (savedRating) setDailyRating(Number.parseInt(savedRating))
  }, [])

  useEffect(() => {
    localStorage.setItem("dailyPlans", JSON.stringify(plans))
  }, [plans])

  useEffect(() => {
    localStorage.setItem("improvements", JSON.stringify(improvements))
  }, [improvements])

  useEffect(() => {
    localStorage.setItem("dailyReflections", JSON.stringify(reflections))
  }, [reflections])

  useEffect(() => {
    localStorage.setItem("dailyRating", dailyRating.toString())
  }, [dailyRating])

  const addPlan = () => {
    if (newPlanTitle.trim()) {
      const newPlan: Plan = {
        id: Date.now().toString(),
        title: newPlanTitle,
        completed: false,
        priority: "medium",
        category: newPlanCategory,
        notes: "",
      }
      setPlans([...plans, newPlan])
      setNewPlanTitle("")
    }
  }

  const updatePlan = (updatedPlan: Plan) => {
    setPlans(plans.map((plan) => (plan.id === updatedPlan.id ? updatedPlan : plan)))
  }

  const deletePlan = (id: string) => {
    setPlans(plans.filter((plan) => plan.id !== id))
  }

  const addImprovement = () => {
    if (newImprovementTitle.trim()) {
      const newImprovement: Improvement = {
        id: Date.now().toString(),
        title: newImprovementTitle,
        completed: false,
      }
      setImprovements([...improvements, newImprovement])
      setNewImprovementTitle("")
    }
  }

  const updateImprovement = (updatedImprovement: Improvement) => {
    setImprovements(improvements.map((imp) => (imp.id === updatedImprovement.id ? updatedImprovement : imp)))
  }

  const deleteImprovement = (id: string) => {
    setImprovements(improvements.filter((imp) => imp.id !== id))
  }

  const addReflection = (reflection: Omit<Reflection, "id">) => {
    const newReflection: Reflection = {
      ...reflection,
      id: Date.now().toString(),
    }
    setReflections([newReflection, ...reflections])
  }

  const updateReflection = (updatedReflection: Reflection) => {
    setReflections(reflections.map((ref) => (ref.id === updatedReflection.id ? updatedReflection : ref)))
  }

  const deleteReflection = (id: string) => {
    setReflections(reflections.filter((ref) => ref.id !== id))
  }

  const completedPlans = plans.filter((plan) => plan.completed).length
  const totalPlans = plans.length

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50">
      <Navigation />

      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* 日期选择器和欢迎信息 */}
        <div className="mb-8 flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent mb-2">
              今日计划管理
            </h1>
            <p className="text-gray-600">让每一天都充满意义和成就感</p>
          </div>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="w-64 justify-start text-left font-normal bg-white shadow-md hover:shadow-lg transition-all duration-200"
              >
                <CalendarIcon className="mr-2 h-4 w-4 text-indigo-500" />
                {format(selectedDate, "yyyy年MM月dd日 EEEE", { locale: zhCN })}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={selectedDate}
                onSelect={(date) => date && setSelectedDate(date)}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 左侧列 - 计划管理 */}
          <div className="lg:col-span-2 space-y-8">
            {/* 统计卡片 */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <CompletionPieChart completed={completedPlans} total={totalPlans} />

              <Card className="bg-gradient-to-br from-yellow-50 to-orange-50 border-yellow-200 shadow-lg hover:shadow-xl transition-all duration-300">
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg flex items-center text-yellow-700">
                    <Sparkles className="h-5 w-5 mr-2" />
                    今日评分
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center">
                    <StarRating rating={dailyRating} onRatingChange={setDailyRating} size="lg" />
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-indigo-50 to-blue-50 border-indigo-200 shadow-lg hover:shadow-xl transition-all duration-300">
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg flex items-center text-indigo-700">
                    <Target className="h-5 w-5 mr-2" />
                    计划统计
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3 text-sm">
                    <div className="flex justify-between items-center">
                      <span className="flex items-center">
                        <Clock className="h-4 w-4 mr-1 text-indigo-500" />
                        总计划:
                      </span>
                      <span className="font-bold text-indigo-600">{totalPlans}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="flex items-center">
                        <CheckCircle className="h-4 w-4 mr-1 text-green-500" />
                        已完成:
                      </span>
                      <span className="font-bold text-green-600">{completedPlans}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="flex items-center">
                        <Target className="h-4 w-4 mr-1 text-orange-500" />
                        待完成:
                      </span>
                      <span className="font-bold text-orange-600">{totalPlans - completedPlans}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* 添加新计划 */}
            <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200 shadow-lg">
              <CardHeader className="bg-gradient-to-r from-blue-500 to-indigo-500 text-white rounded-t-lg">
                <CardTitle className="flex items-center">
                  <Plus className="h-5 w-5 mr-2" />
                  添加新计划
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="flex space-x-3">
                  <Input
                    value={newPlanTitle}
                    onChange={(e) => setNewPlanTitle(e.target.value)}
                    placeholder="输入新的计划..."
                    onKeyPress={(e) => e.key === "Enter" && addPlan()}
                    className="flex-1 border-blue-200 focus:border-blue-400 bg-white/70"
                  />
                  <Select value={newPlanCategory} onValueChange={(value: any) => setNewPlanCategory(value)}>
                    <SelectTrigger className="w-32 border-blue-200 bg-white/70">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="study">学习</SelectItem>
                      <SelectItem value="work">工作</SelectItem>
                      <SelectItem value="life">生活</SelectItem>
                      <SelectItem value="other">其他</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button
                    onClick={addPlan}
                    disabled={!newPlanTitle.trim()}
                    className="bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white shadow-md"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    添加
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* 计划列表 */}
            <Card className="shadow-lg border-gray-200">
              <CardHeader className="bg-gradient-to-r from-gray-50 to-blue-50 border-b">
                <CardTitle className="flex items-center justify-between">
                  <span className="flex items-center text-gray-700">
                    <Target className="h-5 w-5 mr-2" />
                    今日计划列表
                  </span>
                  <div className="text-sm font-normal bg-gradient-to-r from-blue-100 to-indigo-100 px-3 py-1 rounded-full text-blue-700">
                    {completedPlans}/{totalPlans} 已完成
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                {plans.length === 0 ? (
                  <div className="text-center py-12 text-gray-500">
                    <div className="bg-gradient-to-br from-gray-100 to-blue-100 w-24 h-24 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Target className="h-12 w-12 text-gray-400" />
                    </div>
                    <p className="text-lg font-medium mb-2">还没有添加任何计划</p>
                    <p className="text-sm">开始规划你的一天吧！</p>
                  </div>
                ) : (
                  <div className="space-y-2">
                    {plans.map((plan, index) => (
                      <DraggablePlanItem key={plan.id} plan={plan} onUpdate={updatePlan} onDelete={deletePlan} />
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* 进度统计图表 */}
            <Card className="shadow-lg border-gray-200">
              <CardHeader className="bg-gradient-to-r from-emerald-50 to-teal-50 border-b">
                <CardTitle className="flex items-center text-emerald-700">
                  <TrendingUp className="h-5 w-5 mr-2" />
                  完成率趋势分析
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={progressData}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#e0e7ff" />
                      <XAxis
                        dataKey="date"
                        tick={{ fontSize: 12 }}
                        tickFormatter={(value) => {
                          const date = new Date(value)
                          return `${date.getMonth() + 1}/${date.getDate()}`
                        }}
                      />
                      <YAxis tick={{ fontSize: 12 }} domain={[0, 100]} />
                      <Tooltip
                        formatter={(value: number, name: string) => [
                          `${value}${name === "completionRate" ? "%" : "星"}`,
                          name === "completionRate" ? "完成率" : "评分",
                        ]}
                        contentStyle={{
                          backgroundColor: "rgba(255, 255, 255, 0.95)",
                          border: "1px solid #e5e7eb",
                          borderRadius: "8px",
                          boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
                        }}
                      />
                      <Line
                        type="monotone"
                        dataKey="completionRate"
                        stroke="#3b82f6"
                        strokeWidth={3}
                        dot={{ fill: "#3b82f6", strokeWidth: 2, r: 5 }}
                        activeDot={{ r: 7, stroke: "#3b82f6", strokeWidth: 2 }}
                      />
                      <Line
                        type="monotone"
                        dataKey="score"
                        stroke="#10b981"
                        strokeWidth={3}
                        dot={{ fill: "#10b981", strokeWidth: 2, r: 5 }}
                        activeDot={{ r: 7, stroke: "#10b981", strokeWidth: 2 }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
                <div className="flex justify-center space-x-8 mt-4 text-sm">
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 bg-blue-500 rounded-full shadow-sm"></div>
                    <span className="font-medium text-blue-700">完成率</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 bg-emerald-500 rounded-full shadow-sm"></div>
                    <span className="font-medium text-emerald-700">每日评分</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 右侧列 - 改进点和感想 */}
          <div className="space-y-8">
            {/* 下次改进 */}
            <Card className="bg-gradient-to-br from-amber-50 to-yellow-50 border-amber-200 shadow-lg">
              <CardHeader className="bg-gradient-to-r from-amber-500 to-yellow-500 text-white rounded-t-lg">
                <CardTitle className="flex items-center">
                  <Lightbulb className="h-5 w-5 mr-2" />
                  下次改进
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6 space-y-4">
                <div className="flex space-x-2">
                  <Input
                    value={newImprovementTitle}
                    onChange={(e) => setNewImprovementTitle(e.target.value)}
                    placeholder="添加改进点..."
                    onKeyPress={(e) => e.key === "Enter" && addImprovement()}
                    className="flex-1 text-sm border-amber-200 focus:border-amber-400 bg-white/70"
                  />
                  <Button
                    size="sm"
                    onClick={addImprovement}
                    disabled={!newImprovementTitle.trim()}
                    className="bg-gradient-to-r from-amber-500 to-yellow-500 hover:from-amber-600 hover:to-yellow-600 text-white"
                  >
                    <Plus className="h-3 w-3" />
                  </Button>
                </div>

                <div className="space-y-2 max-h-64 overflow-y-auto">
                  {improvements.length === 0 ? (
                    <div className="text-center py-8 text-amber-600">
                      <Lightbulb className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">暂无改进点</p>
                    </div>
                  ) : (
                    improvements.map((improvement) => (
                      <ImprovementItem
                        key={improvement.id}
                        improvement={improvement}
                        onUpdate={updateImprovement}
                        onDelete={deleteImprovement}
                      />
                    ))
                  )}
                </div>
              </CardContent>
            </Card>

            {/* 每日感想 */}
            <div className="space-y-4">
              <DailyReflection
                reflections={reflections}
                onAddReflection={addReflection}
                onUpdateReflection={updateReflection}
                onDeleteReflection={deleteReflection}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
