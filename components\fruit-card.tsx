"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { CheckCircle, MessageCircle, Heart, Plus, X } from "lucide-react"

interface Fruit {
  id: string
  name: string
  emoji: string
  color: string
  nutrition: {
    calories: number
    vitamin: string
    fiber: number
  }
  benefits: string[]
}

interface DayFruit {
  fruitId: string
  completed: boolean
  comment: string
  rating: number
}

interface FruitCardProps {
  day: string
  dayName: string
  fruits: DayFruit[]
  availableFruits: Fruit[]
  onAddFruit: (day: string, fruitId: string) => void
  onRemoveFruit: (day: string, fruitId: string) => void
  onToggleComplete: (day: string, fruitId: string) => void
  onUpdateComment: (day: string, fruitId: string, comment: string) => void
  onUpdateRating: (day: string, fruitId: string, rating: number) => void
}

export function FruitCard({
  day,
  dayName,
  fruits,
  availableFruits,
  onAddFruit,
  onRemoveFruit,
  onToggleComplete,
  onUpdateComment,
  onUpdateRating,
}: FruitCardProps) {
  const [showAddFruit, setShowAddFruit] = useState(false)
  const [editingComment, setEditingComment] = useState<string | null>(null)
  const [tempComment, setTempComment] = useState("")

  const completedCount = fruits.filter((f) => f.completed).length
  const totalCount = fruits.length
  const completionRate = totalCount > 0 ? (completedCount / totalCount) * 100 : 0

  const handleAddFruit = (fruitId: string) => {
    onAddFruit(day, fruitId)
    setShowAddFruit(false)
  }

  const handleEditComment = (fruitId: string, currentComment: string) => {
    setEditingComment(fruitId)
    setTempComment(currentComment)
  }

  const handleSaveComment = (fruitId: string) => {
    onUpdateComment(day, fruitId, tempComment)
    setEditingComment(null)
    setTempComment("")
  }

  const getFruitInfo = (fruitId: string) => {
    return availableFruits.find((f) => f.id === fruitId)
  }

  return (
    <Card className="bg-gradient-to-br from-white to-orange-50 border-orange-200 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
      <CardContent className="p-4">
        {/* 日期头部 */}
        <div className="flex items-center justify-between mb-3">
          <div>
            <h3 className="text-lg font-bold text-gray-800">{dayName}</h3>
            <p className="text-sm text-gray-600">{day}</p>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-orange-600">
              {completedCount}/{totalCount}
            </div>
            <div className="text-xs text-gray-500">完成率 {Math.round(completionRate)}%</div>
          </div>
        </div>

        {/* 进度条 */}
        <div className="w-full bg-gray-200 rounded-full h-2 mb-3">
          <div
            className="bg-gradient-to-r from-orange-400 to-red-400 h-2 rounded-full transition-all duration-500"
            style={{ width: `${completionRate}%` }}
          ></div>
        </div>

        {/* 水果列表 */}
        <div className="space-y-2 mb-3">
          {fruits.map((dayFruit) => {
            const fruit = getFruitInfo(dayFruit.fruitId)
            if (!fruit) return null

            return (
              <div
                key={dayFruit.fruitId}
                className={`p-2 rounded-lg border-2 transition-all duration-200 ${
                  dayFruit.completed
                    ? "bg-gradient-to-r from-green-50 to-emerald-50 border-green-200"
                    : "bg-white border-gray-200 hover:border-orange-300"
                }`}
              >
                <div className="flex items-center justify-between mb-1">
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => onToggleComplete(day, dayFruit.fruitId)}
                      className={`w-5 h-5 rounded-full border-2 flex items-center justify-center transition-all duration-200 ${
                        dayFruit.completed
                          ? "bg-green-500 border-green-500 text-white"
                          : "border-gray-300 hover:border-green-400"
                      }`}
                    >
                      {dayFruit.completed && <CheckCircle className="h-3 w-3" />}
                    </button>
                    <span className="text-lg">{fruit.emoji}</span>
                    <div>
                      <span
                        className={`font-medium text-sm ${dayFruit.completed ? "line-through text-gray-500" : "text-gray-800"}`}
                      >
                        {fruit.name}
                      </span>
                      <div className="flex items-center space-x-1 mt-0.5">
                        <Badge className="text-xs bg-orange-100 text-orange-800">{fruit.nutrition.calories}卡</Badge>
                        <Badge className="text-xs bg-green-100 text-green-800">{fruit.nutrition.vitamin}</Badge>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-1">
                    {/* 星级评分 */}
                    <div className="flex space-x-0.5">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <button
                          key={star}
                          onClick={() => onUpdateRating(day, dayFruit.fruitId, star)}
                          className="transition-colors duration-150"
                        >
                          <Heart
                            className={`h-3 w-3 ${
                              star <= dayFruit.rating ? "fill-red-400 text-red-400" : "text-gray-300"
                            }`}
                          />
                        </button>
                      ))}
                    </div>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => handleEditComment(dayFruit.fruitId, dayFruit.comment)}
                      className="text-blue-600 hover:bg-blue-50 p-1"
                    >
                      <MessageCircle className="h-3 w-3" />
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => onRemoveFruit(day, dayFruit.fruitId)}
                      className="text-red-600 hover:bg-red-50 p-1"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                </div>

                {/* 评论区域 */}
                {editingComment === dayFruit.fruitId ? (
                  <div className="mt-2 space-y-2">
                    <Textarea
                      value={tempComment}
                      onChange={(e) => setTempComment(e.target.value)}
                      placeholder="分享你的感受..."
                      rows={2}
                      className="text-sm"
                    />
                    <div className="flex space-x-2">
                      <Button size="sm" onClick={() => handleSaveComment(dayFruit.fruitId)}>
                        保存
                      </Button>
                      <Button size="sm" variant="outline" onClick={() => setEditingComment(null)}>
                        取消
                      </Button>
                    </div>
                  </div>
                ) : dayFruit.comment ? (
                  <div className="mt-2 p-2 bg-blue-50 rounded text-sm text-gray-700 border-l-2 border-blue-300">
                    {dayFruit.comment}
                  </div>
                ) : null}
              </div>
            )
          })}
        </div>

        {/* 添加水果 */}
        {showAddFruit ? (
          <div className="space-y-2">
            <div className="grid grid-cols-2 gap-2 max-h-32 overflow-y-auto">
              {availableFruits
                .filter((fruit) => !fruits.some((f) => f.fruitId === fruit.id))
                .map((fruit) => (
                  <button
                    key={fruit.id}
                    onClick={() => handleAddFruit(fruit.id)}
                    className="p-2 rounded-lg border border-gray-200 hover:border-orange-300 hover:bg-orange-50 transition-all duration-200 flex items-center space-x-2 text-left"
                  >
                    <span className="text-lg">{fruit.emoji}</span>
                    <span className="text-sm font-medium">{fruit.name}</span>
                  </button>
                ))}
            </div>
            <Button size="sm" variant="outline" onClick={() => setShowAddFruit(false)} className="w-full">
              取消
            </Button>
          </div>
        ) : (
          <Button
            onClick={() => setShowAddFruit(true)}
            className="w-full bg-gradient-to-r from-orange-400 to-red-400 hover:from-orange-500 hover:to-red-500 text-white"
          >
            <Plus className="h-4 w-4 mr-2" />
            添加水果
          </Button>
        )}
      </CardContent>
    </Card>
  )
}
