"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Award, Edit2, Save, Plus, Trash2 } from "lucide-react"

interface HealthTip {
  id: string
  title: string
  content: string
  color: string
}

interface EditableHealthTipsProps {
  tips: HealthTip[]
  onUpdateTips: (tips: HealthTip[]) => void
}

export function EditableHealthTips({ tips, onUpdateTips }: EditableHealthTipsProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editingTips, setEditingTips] = useState<HealthTip[]>(tips)
  const [showAddForm, setShowAddForm] = useState(false)
  const [newTip, setNewTip] = useState({ title: "", content: "", color: "blue" })

  const handleSave = () => {
    onUpdateTips(editingTips)
    setIsEditing(false)
  }

  const handleCancel = () => {
    setEditingTips(tips)
    setIsEditing(false)
    setShowAddForm(false)
  }

  const handleUpdateTip = (index: number, field: keyof HealthTip, value: string) => {
    const updated = [...editingTips]
    updated[index] = { ...updated[index], [field]: value }
    setEditingTips(updated)
  }

  const handleAddNew = () => {
    if (newTip.title && newTip.content) {
      const newHealthTip: HealthTip = {
        id: Date.now().toString(),
        ...newTip,
      }
      setEditingTips([...editingTips, newHealthTip])
      setNewTip({ title: "", content: "", color: "blue" })
      setShowAddForm(false)
    }
  }

  const handleDelete = (index: number) => {
    const updated = editingTips.filter((_, i) => i !== index)
    setEditingTips(updated)
  }

  const getColorClasses = (color: string) => {
    switch (color) {
      case "blue":
        return "border-blue-400 bg-white"
      case "green":
        return "border-green-400 bg-white"
      case "orange":
        return "border-orange-400 bg-white"
      case "purple":
        return "border-purple-400 bg-white"
      default:
        return "border-blue-400 bg-white"
    }
  }

  const getTitleColor = (color: string) => {
    switch (color) {
      case "blue":
        return "text-blue-800"
      case "green":
        return "text-green-800"
      case "orange":
        return "text-orange-800"
      case "purple":
        return "text-purple-800"
      default:
        return "text-blue-800"
    }
  }

  const getContentColor = (color: string) => {
    switch (color) {
      case "blue":
        return "text-blue-700"
      case "green":
        return "text-green-700"
      case "orange":
        return "text-orange-700"
      case "purple":
        return "text-purple-700"
      default:
        return "text-blue-700"
    }
  }

  return (
    <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200">
      <CardHeader>
        <CardTitle className="flex items-center justify-between text-lg">
          <span className="flex items-center">
            <Award className="h-5 w-5 mr-2 text-blue-500" />
            健康小知识
          </span>
          <Button
            size="sm"
            variant="outline"
            onClick={() => setIsEditing(!isEditing)}
            className="border-blue-300 text-blue-600 hover:bg-blue-50"
          >
            <Edit2 className="h-3 w-3 mr-1" />
            {isEditing ? "取消" : "编辑"}
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3 text-sm">
        {isEditing ? (
          <div className="space-y-4">
            {editingTips.map((tip, index) => (
              <div key={tip.id} className="p-3 bg-white rounded-lg border space-y-2">
                <div className="flex items-center space-x-2">
                  <Input
                    value={tip.title}
                    onChange={(e) => handleUpdateTip(index, "title", e.target.value)}
                    placeholder="标题"
                    className="flex-1"
                  />
                  <select
                    value={tip.color}
                    onChange={(e) => handleUpdateTip(index, "color", e.target.value)}
                    className="px-2 py-1 border rounded text-sm"
                  >
                    <option value="blue">蓝色</option>
                    <option value="green">绿色</option>
                    <option value="orange">橙色</option>
                    <option value="purple">紫色</option>
                  </select>
                  <Button size="sm" variant="ghost" onClick={() => handleDelete(index)} className="text-red-600">
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
                <Textarea
                  value={tip.content}
                  onChange={(e) => handleUpdateTip(index, "content", e.target.value)}
                  placeholder="内容"
                  rows={2}
                  className="text-sm"
                />
              </div>
            ))}

            {showAddForm && (
              <div className="p-3 bg-blue-50 rounded-lg border border-blue-200 space-y-2">
                <div className="flex items-center space-x-2">
                  <Input
                    value={newTip.title}
                    onChange={(e) => setNewTip({ ...newTip, title: e.target.value })}
                    placeholder="标题"
                    className="flex-1"
                  />
                  <select
                    value={newTip.color}
                    onChange={(e) => setNewTip({ ...newTip, color: e.target.value })}
                    className="px-2 py-1 border rounded text-sm"
                  >
                    <option value="blue">蓝色</option>
                    <option value="green">绿色</option>
                    <option value="orange">橙色</option>
                    <option value="purple">紫色</option>
                  </select>
                </div>
                <Textarea
                  value={newTip.content}
                  onChange={(e) => setNewTip({ ...newTip, content: e.target.value })}
                  placeholder="内容"
                  rows={2}
                  className="text-sm"
                />
                <div className="flex space-x-2">
                  <Button size="sm" onClick={handleAddNew}>
                    添加
                  </Button>
                  <Button size="sm" variant="outline" onClick={() => setShowAddForm(false)}>
                    取消
                  </Button>
                </div>
              </div>
            )}

            <div className="flex space-x-2">
              <Button size="sm" onClick={handleSave} className="bg-blue-500 hover:bg-blue-600">
                <Save className="h-3 w-3 mr-1" />
                保存更改
              </Button>
              <Button size="sm" variant="outline" onClick={handleCancel}>
                取消
              </Button>
              {!showAddForm && (
                <Button size="sm" variant="outline" onClick={() => setShowAddForm(true)}>
                  <Plus className="h-3 w-3 mr-1" />
                  添加知识
                </Button>
              )}
            </div>
          </div>
        ) : (
          <div className="space-y-3">
            {tips.map((tip) => (
              <div key={tip.id} className={`p-3 rounded-lg border-l-4 ${getColorClasses(tip.color)}`}>
                <div className={`font-medium mb-1 ${getTitleColor(tip.color)}`}>{tip.title}</div>
                <div className={getContentColor(tip.color)}>{tip.content}</div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
