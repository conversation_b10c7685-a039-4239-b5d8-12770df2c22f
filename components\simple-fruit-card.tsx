"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { CheckCircle, MessageCircle, Heart, Plus, X } from "lucide-react"

interface DayFruit {
  id: string
  name: string
  completed: boolean
  comment: string
  rating: number
}

interface SimpleFruitCardProps {
  day: string
  dayName: string
  fruits: DayFruit[]
  onAddFruit: (day: string, name: string) => void
  onRemoveFruit: (day: string, fruitId: string) => void
  onToggleComplete: (day: string, fruitId: string) => void
  onUpdateComment: (day: string, fruitId: string, comment: string) => void
  onUpdateRating: (day: string, fruitId: string, rating: number) => void
}

export function SimpleFruitCard({
  day,
  dayName,
  fruits,
  onAddFruit,
  onRemoveFruit,
  onToggleComplete,
  onUpdateComment,
  onUpdateRating,
}: SimpleFruitCardProps) {
  const [showAddForm, setShowAddForm] = useState(false)
  const [newFruitName, setNewFruitName] = useState("")
  const [editingComment, setEditingComment] = useState<string | null>(null)
  const [tempComment, setTempComment] = useState("")

  const completedCount = fruits.filter((f) => f.completed).length
  const totalCount = fruits.length
  const completionRate = totalCount > 0 ? (completedCount / totalCount) * 100 : 0

  const handleAddFruit = () => {
    if (newFruitName.trim()) {
      onAddFruit(day, newFruitName.trim())
      setNewFruitName("")
      setShowAddForm(false)
    }
  }

  const handleEditComment = (fruitId: string, currentComment: string) => {
    setEditingComment(fruitId)
    setTempComment(currentComment)
  }

  const handleSaveComment = (fruitId: string) => {
    onUpdateComment(day, fruitId, tempComment)
    setEditingComment(null)
    setTempComment("")
  }

  return (
    <Card className="bg-gradient-to-br from-white to-orange-50 border-orange-200 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
      <CardContent className="p-4">
        {/* 日期头部 */}
        <div className="flex items-center justify-between mb-3">
          <div>
            <h3 className="text-lg font-bold text-gray-800">{dayName}</h3>
            <p className="text-sm text-gray-600">{day}</p>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-orange-600">
              {completedCount}/{totalCount}
            </div>
            <div className="text-xs text-gray-500">完成率 {Math.round(completionRate)}%</div>
          </div>
        </div>

        {/* 进度条 */}
        <div className="w-full bg-gray-200 rounded-full h-2 mb-3">
          <div
            className="bg-gradient-to-r from-orange-400 to-red-400 h-2 rounded-full transition-all duration-500"
            style={{ width: `${completionRate}%` }}
          ></div>
        </div>

        {/* 水果列表 */}
        <div className="space-y-2 mb-3">
          {fruits.map((fruit) => (
            <div
              key={fruit.id}
              className={`p-3 rounded-lg border-2 transition-all duration-200 ${
                fruit.completed
                  ? "bg-gradient-to-r from-green-50 to-emerald-50 border-green-200"
                  : "bg-white border-gray-200 hover:border-orange-300"
              }`}
            >
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-3">
                  <button
                    onClick={() => onToggleComplete(day, fruit.id)}
                    className={`w-5 h-5 rounded-full border-2 flex items-center justify-center transition-all duration-200 ${
                      fruit.completed
                        ? "bg-green-500 border-green-500 text-white"
                        : "border-gray-300 hover:border-green-400"
                    }`}
                  >
                    {fruit.completed && <CheckCircle className="h-3 w-3" />}
                  </button>
                  <div>
                    <span className={`font-medium ${fruit.completed ? "line-through text-gray-500" : "text-gray-800"}`}>
                      {fruit.name}
                    </span>
                  </div>
                </div>
                <div className="flex items-center space-x-1">
                  {/* 星级评分 */}
                  <div className="flex space-x-0.5">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <button
                        key={star}
                        onClick={() => onUpdateRating(day, fruit.id, star)}
                        className="transition-colors duration-150"
                      >
                        <Heart
                          className={`h-3 w-3 ${star <= fruit.rating ? "fill-red-400 text-red-400" : "text-gray-300"}`}
                        />
                      </button>
                    ))}
                  </div>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => handleEditComment(fruit.id, fruit.comment)}
                    className="text-blue-600 hover:bg-blue-50 p-1"
                  >
                    <MessageCircle className="h-3 w-3" />
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => onRemoveFruit(day, fruit.id)}
                    className="text-red-600 hover:bg-red-50 p-1"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              </div>

              {/* 评论区域 */}
              {editingComment === fruit.id ? (
                <div className="mt-2 space-y-2">
                  <Textarea
                    value={tempComment}
                    onChange={(e) => setTempComment(e.target.value)}
                    placeholder="分享你的感受..."
                    rows={2}
                    className="text-sm"
                  />
                  <div className="flex space-x-2">
                    <Button size="sm" onClick={() => handleSaveComment(fruit.id)}>
                      保存
                    </Button>
                    <Button size="sm" variant="outline" onClick={() => setEditingComment(null)}>
                      取消
                    </Button>
                  </div>
                </div>
              ) : fruit.comment ? (
                <div className="mt-2 p-2 bg-blue-50 rounded text-sm text-gray-700 border-l-2 border-blue-300">
                  {fruit.comment}
                </div>
              ) : null}
            </div>
          ))}
        </div>

        {/* 添加水果 */}
        {showAddForm ? (
          <div className="space-y-3">
            <Input
              value={newFruitName}
              onChange={(e) => setNewFruitName(e.target.value)}
              placeholder="输入水果或食物名称..."
              onKeyPress={(e) => e.key === "Enter" && handleAddFruit()}
              className="border-orange-200 focus:border-orange-400"
            />
            <div className="flex space-x-2">
              <Button
                onClick={handleAddFruit}
                disabled={!newFruitName.trim()}
                className="flex-1 bg-gradient-to-r from-orange-400 to-red-400 hover:from-orange-500 hover:to-red-500 text-white"
              >
                添加
              </Button>
              <Button variant="outline" onClick={() => setShowAddForm(false)} className="flex-1">
                取消
              </Button>
            </div>
          </div>
        ) : (
          <Button
            onClick={() => setShowAddForm(true)}
            className="w-full bg-gradient-to-r from-orange-400 to-red-400 hover:from-orange-500 hover:to-red-500 text-white"
          >
            <Plus className="h-4 w-4 mr-2" />
            添加水果
          </Button>
        )}
      </CardContent>
    </Card>
  )
}
