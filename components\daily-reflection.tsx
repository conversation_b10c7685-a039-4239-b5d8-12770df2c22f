"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Heart, MessageCircle, ImageIcon, Calendar, Edit2, Trash2, Save, X } from "lucide-react"

interface Reflection {
  id: string
  date: string
  content: string
  mood: "happy" | "neutral" | "sad"
  images: string[]
}

interface DailyReflectionProps {
  reflections: Reflection[]
  onAddReflection: (reflection: Omit<Reflection, "id">) => void
  onUpdateReflection: (reflection: Reflection) => void
  onDeleteReflection: (id: string) => void
}

export function DailyReflection({
  reflections,
  onAddReflection,
  onUpdateReflection,
  onDeleteReflection,
}: DailyReflectionProps) {
  const [newReflection, setNewReflection] = useState("")
  const [selectedMood, setSelectedMood] = useState<"happy" | "neutral" | "sad">("neutral")
  const [editingId, setEditingId] = useState<string | null>(null)
  const [editContent, setEditContent] = useState("")

  const handleSubmit = () => {
    if (newReflection.trim()) {
      onAddReflection({
        date: new Date().toISOString(),
        content: newReflection,
        mood: selectedMood,
        images: [],
      })
      setNewReflection("")
      setSelectedMood("neutral")
    }
  }

  const handleEdit = (reflection: Reflection) => {
    setEditingId(reflection.id)
    setEditContent(reflection.content)
  }

  const handleSaveEdit = (reflection: Reflection) => {
    onUpdateReflection({
      ...reflection,
      content: editContent,
    })
    setEditingId(null)
    setEditContent("")
  }

  const handleCancelEdit = () => {
    setEditingId(null)
    setEditContent("")
  }

  const getMoodEmoji = (mood: string) => {
    switch (mood) {
      case "happy":
        return "😊"
      case "neutral":
        return "😐"
      case "sad":
        return "😔"
      default:
        return "😐"
    }
  }

  const getMoodColor = (mood: string) => {
    switch (mood) {
      case "happy":
        return "bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border-green-200"
      case "neutral":
        return "bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800 border-gray-200"
      case "sad":
        return "bg-gradient-to-r from-blue-100 to-sky-100 text-blue-800 border-blue-200"
      default:
        return "bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800 border-gray-200"
    }
  }

  return (
    <div className="space-y-6">
      {/* 添加新感想 */}
      <Card className="bg-gradient-to-br from-purple-50 to-pink-50 border-purple-200 shadow-lg">
        <CardHeader className="bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-t-lg">
          <CardTitle className="flex items-center space-x-2">
            <MessageCircle className="h-5 w-5" />
            <span>今日感想</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6 space-y-4">
          <Textarea
            value={newReflection}
            onChange={(e) => setNewReflection(e.target.value)}
            placeholder="记录今天的想法、感受或收获..."
            rows={4}
            className="resize-none border-purple-200 focus:border-purple-400 bg-white/70"
          />

          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
            <div className="flex items-center space-x-4">
              <span className="text-sm font-medium text-purple-700">心情:</span>
              <div className="flex space-x-2">
                {(["happy", "neutral", "sad"] as const).map((mood) => (
                  <button
                    key={mood}
                    onClick={() => setSelectedMood(mood)}
                    className={`p-3 rounded-full text-xl transition-all duration-200 transform hover:scale-110 ${
                      selectedMood === mood ? "bg-purple-200 ring-2 ring-purple-400 shadow-md" : "hover:bg-purple-100"
                    }`}
                  >
                    {getMoodEmoji(mood)}
                  </button>
                ))}
              </div>
            </div>

            <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 w-full sm:w-auto">
              <Button
                variant="outline"
                size="sm"
                className="border-purple-300 text-purple-600 hover:bg-purple-50 bg-transparent w-full sm:w-auto"
              >
                <ImageIcon className="h-4 w-4 mr-2" />
                添加图片
              </Button>
              <Button
                onClick={handleSubmit}
                disabled={!newReflection.trim()}
                className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white shadow-md w-full sm:w-auto"
              >
                发布感想
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 历史感想 */}
      <div className="space-y-4">
        {reflections.map((reflection) => (
          <Card
            key={reflection.id}
            className="hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 bg-gradient-to-br from-white to-gray-50 border-gray-200"
          >
            <CardContent className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <Calendar className="h-4 w-4 text-indigo-500" />
                  <span className="text-sm font-medium text-gray-600">
                    {new Date(reflection.date).toLocaleDateString("zh-CN", {
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                      weekday: "long",
                    })}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge className={`${getMoodColor(reflection.mood)} border`}>
                    {getMoodEmoji(reflection.mood)}{" "}
                    {reflection.mood === "happy" ? "开心" : reflection.mood === "neutral" ? "平静" : "低落"}
                  </Badge>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => handleEdit(reflection)}
                    className="text-gray-500 hover:text-indigo-600"
                  >
                    <Edit2 className="h-3 w-3" />
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => onDeleteReflection(reflection.id)}
                    className="text-gray-500 hover:text-red-600"
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
              </div>

              {editingId === reflection.id ? (
                <div className="space-y-3">
                  <Textarea
                    value={editContent}
                    onChange={(e) => setEditContent(e.target.value)}
                    rows={3}
                    className="resize-none"
                  />
                  <div className="flex space-x-2">
                    <Button size="sm" onClick={() => handleSaveEdit(reflection)}>
                      <Save className="h-3 w-3 mr-1" />
                      保存
                    </Button>
                    <Button size="sm" variant="outline" onClick={handleCancelEdit}>
                      <X className="h-3 w-3 mr-1" />
                      取消
                    </Button>
                  </div>
                </div>
              ) : (
                <>
                  <p className="text-gray-800 leading-relaxed mb-4 bg-gradient-to-r from-gray-50 to-blue-50 p-4 rounded-lg border-l-4 border-indigo-400">
                    {reflection.content}
                  </p>

                  <div className="flex items-center space-x-6 text-sm text-gray-500">
                    <button className="flex items-center space-x-2 hover:text-red-500 transition-colors duration-200 group">
                      <Heart className="h-4 w-4 group-hover:fill-current" />
                      <span>点赞</span>
                    </button>
                    <button className="flex items-center space-x-2 hover:text-blue-500 transition-colors duration-200">
                      <MessageCircle className="h-4 w-4" />
                      <span>评论</span>
                    </button>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
