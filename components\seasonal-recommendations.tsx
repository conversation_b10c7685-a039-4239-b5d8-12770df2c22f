"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON>, Snowflake, <PERSON>, Info } from "lucide-react"

interface SeasonalRecommendationsProps {
  onAddRecommendation: (fruitId: string) => void
}

export function SeasonalRecommendations({ onAddRecommendation }: SeasonalRecommendationsProps) {
  const currentMonth = new Date().getMonth() + 1

  const getSeasonIcon = () => {
    if (currentMonth >= 3 && currentMonth <= 5) return <Flower className="h-5 w-5 text-green-500" />
    if (currentMonth >= 6 && currentMonth <= 8) return <Sun className="h-5 w-5 text-yellow-500" />
    if (currentMonth >= 9 && currentMonth <= 11) return <Leaf className="h-5 w-5 text-orange-500" />
    return <Snowflake className="h-5 w-5 text-blue-500" />
  }

  const getSeasonName = () => {
    if (currentMonth >= 3 && currentMonth <= 5) return "春季"
    if (currentMonth >= 6 && currentMonth <= 8) return "夏季"
    if (currentMonth >= 9 && currentMonth <= 11) return "秋季"
    return "冬季"
  }

  const getSeasonalFruits = () => {
    if (currentMonth >= 3 && currentMonth <= 5) {
      return [
        { id: "strawberry", name: "草莓", emoji: "🍓", reason: "富含维生素C，春季时令" },
        { id: "pineapple", name: "菠萝", emoji: "🍍", reason: "帮助消化，清热解毒" },
        { id: "cherry", name: "樱桃", emoji: "🍒", reason: "抗氧化，美容养颜" },
      ]
    }
    if (currentMonth >= 6 && currentMonth <= 8) {
      return [
        { id: "watermelon", name: "西瓜", emoji: "🍉", reason: "清热解暑，补充水分" },
        { id: "peach", name: "桃子", emoji: "🍑", reason: "润肺生津，夏季佳品" },
        { id: "mango", name: "芒果", emoji: "🥭", reason: "维生素A丰富，护眼明目" },
      ]
    }
    if (currentMonth >= 9 && currentMonth <= 11) {
      return [
        { id: "apple", name: "苹果", emoji: "🍎", reason: "秋季养肺，营养均衡" },
        { id: "pear", name: "梨", emoji: "🍐", reason: "润燥止咳，秋季必备" },
        { id: "grape", name: "葡萄", emoji: "🍇", reason: "抗氧化，秋季时令" },
      ]
    }
    return [
      { id: "orange", name: "橙子", emoji: "🍊", reason: "维生素C丰富，预防感冒" },
      { id: "kiwi", name: "猕猴桃", emoji: "🥝", reason: "营养密度高，冬季补充" },
      { id: "banana", name: "香蕉", emoji: "🍌", reason: "补充能量，四季皆宜" },
    ]
  }

  const seasonalFruits = getSeasonalFruits()

  return (
    <Card className="bg-gradient-to-br from-green-50 to-blue-50 border-green-200">
      <CardHeader>
        <CardTitle className="flex items-center text-lg">
          {getSeasonIcon()}
          <span className="ml-2">{getSeasonName()}时令推荐</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {seasonalFruits.map((fruit) => (
          <div
            key={fruit.id}
            className="flex items-center justify-between p-3 bg-white rounded-lg border border-green-100"
          >
            <div className="flex items-center space-x-3">
              <span className="text-2xl">{fruit.emoji}</span>
              <div>
                <div className="font-medium text-gray-800">{fruit.name}</div>
                <div className="text-sm text-gray-600 flex items-center">
                  <Info className="h-3 w-3 mr-1" />
                  {fruit.reason}
                </div>
              </div>
            </div>
            <Button
              size="sm"
              onClick={() => onAddRecommendation(fruit.id)}
              className="bg-gradient-to-r from-green-400 to-blue-400 hover:from-green-500 hover:to-blue-500 text-white"
            >
              添加
            </Button>
          </div>
        ))}

        <div className="mt-4 p-3 bg-gradient-to-r from-green-100 to-blue-100 rounded-lg">
          <div className="text-sm font-medium text-green-800 mb-1">💡 健康小贴士</div>
          <div className="text-xs text-green-700">
            {getSeasonName()}是
            {getSeasonName() === "春季"
              ? "万物复苏"
              : getSeasonName() === "夏季"
                ? "阳气旺盛"
                : getSeasonName() === "秋季"
                  ? "收获养肺"
                  : "温补养生"}
            的季节， 多吃时令水果有助于顺应自然规律，保持身体健康。
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
