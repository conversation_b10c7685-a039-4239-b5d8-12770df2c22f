"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Edit2, Trash2, GripVertical, Save, X, BookOpen, Briefcase, Home, MoreHorizontal } from "lucide-react"

interface Plan {
  id: string
  title: string
  completed: boolean
  priority: "high" | "medium" | "low"
  category: "study" | "work" | "life" | "other"
  notes: string
}

interface DraggablePlanItemProps {
  plan: Plan
  onUpdate: (plan: Plan) => void
  onDelete: (id: string) => void
  isDragging?: boolean
}

export function DraggablePlanItem({ plan, onUpdate, onDelete, isDragging }: DraggablePlanItemProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editTitle, setEditTitle] = useState(plan.title)
  const [editNotes, setEditNotes] = useState(plan.notes)
  const [editPriority, setEditPriority] = useState(plan.priority)
  const [editCategory, setEditCategory] = useState(plan.category)

  const handleSave = () => {
    onUpdate({
      ...plan,
      title: editTitle,
      notes: editNotes,
      priority: editPriority,
      category: editCategory,
    })
    setIsEditing(false)
  }

  const handleCancel = () => {
    setEditTitle(plan.title)
    setEditNotes(plan.notes)
    setEditPriority(plan.priority)
    setEditCategory(plan.category)
    setIsEditing(false)
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "bg-gradient-to-r from-red-100 to-pink-100 text-red-800 border-red-200"
      case "medium":
        return "bg-gradient-to-r from-yellow-100 to-orange-100 text-yellow-800 border-yellow-200"
      case "low":
        return "bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border-green-200"
      default:
        return "bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800 border-gray-200"
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case "study":
        return "bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 border-blue-200"
      case "work":
        return "bg-gradient-to-r from-purple-100 to-violet-100 text-purple-800 border-purple-200"
      case "life":
        return "bg-gradient-to-r from-green-100 to-teal-100 text-green-800 border-green-200"
      case "other":
        return "bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800 border-gray-200"
      default:
        return "bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800 border-gray-200"
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "study":
        return <BookOpen className="h-3 w-3" />
      case "work":
        return <Briefcase className="h-3 w-3" />
      case "life":
        return <Home className="h-3 w-3" />
      case "other":
        return <MoreHorizontal className="h-3 w-3" />
      default:
        return <MoreHorizontal className="h-3 w-3" />
    }
  }

  const getCategoryName = (category: string) => {
    switch (category) {
      case "study":
        return "学习"
      case "work":
        return "工作"
      case "life":
        return "生活"
      case "other":
        return "其他"
      default:
        return "其他"
    }
  }

  return (
    <Card
      className={`mb-3 transition-all duration-300 transform hover:scale-[1.02] ${
        plan.completed
          ? "opacity-75 bg-gradient-to-r from-gray-50 to-slate-50"
          : "hover:shadow-lg bg-gradient-to-r from-white to-gray-50"
      } ${isDragging ? "shadow-2xl rotate-2 scale-105" : ""} border-l-4 ${
        plan.completed ? "border-l-gray-300" : "border-l-indigo-400"
      }`}
    >
      <CardContent className="p-4">
        <div className="flex items-start space-x-3">
          <div className="flex items-center space-x-2 mt-1">
            <div className="cursor-grab active:cursor-grabbing p-1 rounded hover:bg-gray-100 transition-colors">
              <GripVertical className="h-4 w-4 text-gray-400" />
            </div>
            <Checkbox
              checked={plan.completed}
              onCheckedChange={(checked) => onUpdate({ ...plan, completed: !!checked })}
              className="data-[state=checked]:bg-emerald-500 data-[state=checked]:border-emerald-500"
            />
          </div>

          <div className="flex-1">
            {isEditing ? (
              <div className="space-y-3">
                <Input
                  value={editTitle}
                  onChange={(e) => setEditTitle(e.target.value)}
                  placeholder="计划标题"
                  className="border-indigo-200 focus:border-indigo-400"
                />
                <Textarea
                  value={editNotes}
                  onChange={(e) => setEditNotes(e.target.value)}
                  placeholder="备注或完成情况"
                  rows={2}
                  className="border-indigo-200 focus:border-indigo-400"
                />
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-gray-600">类别:</span>
                    <Select value={editCategory} onValueChange={(value: any) => setEditCategory(value)}>
                      <SelectTrigger className="w-24">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="study">学习</SelectItem>
                        <SelectItem value="work">工作</SelectItem>
                        <SelectItem value="life">生活</SelectItem>
                        <SelectItem value="other">其他</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-gray-600">优先级:</span>
                    <Select value={editPriority} onValueChange={(value: any) => setEditPriority(value)}>
                      <SelectTrigger className="w-20">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="high">高</SelectItem>
                        <SelectItem value="medium">中</SelectItem>
                        <SelectItem value="low">低</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            ) : (
              <div>
                <div className="flex items-center space-x-2 mb-2">
                  <h3 className={`font-medium ${plan.completed ? "line-through text-gray-500" : "text-gray-900"}`}>
                    {plan.title}
                  </h3>
                  <Badge className={`${getCategoryColor(plan.category)} border flex items-center space-x-1`}>
                    {getCategoryIcon(plan.category)}
                    <span>{getCategoryName(plan.category)}</span>
                  </Badge>
                  <Badge className={`${getPriorityColor(plan.priority)} border`}>
                    {plan.priority === "high" ? "高优先级" : plan.priority === "medium" ? "中优先级" : "低优先级"}
                  </Badge>
                </div>
                {plan.notes && (
                  <p className="text-sm text-gray-600 mt-2 bg-gray-50 p-2 rounded-md border-l-2 border-indigo-200">
                    {plan.notes}
                  </p>
                )}
              </div>
            )}
          </div>

          <div className="flex items-center space-x-1">
            {isEditing ? (
              <>
                <Button size="sm" variant="ghost" onClick={handleSave} className="text-green-600 hover:bg-green-50">
                  <Save className="h-4 w-4" />
                </Button>
                <Button size="sm" variant="ghost" onClick={handleCancel} className="text-gray-600 hover:bg-gray-50">
                  <X className="h-4 w-4" />
                </Button>
              </>
            ) : (
              <>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => setIsEditing(true)}
                  className="text-indigo-600 hover:bg-indigo-50"
                >
                  <Edit2 className="h-4 w-4" />
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => onDelete(plan.id)}
                  className="text-red-600 hover:bg-red-50"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
