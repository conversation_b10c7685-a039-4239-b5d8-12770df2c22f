"use client"

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>hart, Pie, Cell, ResponsiveContainer, Tooltip, BarChart, Bar, XAxis, YAxis, CartesianGrid } from "recharts"
import { TrendingUp, Award, Target } from "lucide-react"

interface Fruit {
  id: string
  name: string
  emoji: string
  color: string
  nutrition: {
    calories: number
    vitamin: string
    fiber: number
  }
  benefits: string[]
}

interface DayFruit {
  fruitId: string
  completed: boolean
  comment: string
  rating: number
}

interface FruitStatsProps {
  weekData: Record<string, DayFruit[]>
  availableFruits: Fruit[]
}

export function FruitStats({ weekData, availableFruits }: FruitStatsProps) {
  // 计算统计数据
  const totalPlanned = Object.values(weekData).reduce((sum, day) => sum + day.length, 0)
  const totalCompleted = Object.values(weekData).reduce((sum, day) => sum + day.filter((f) => f.completed).length, 0)
  const completionRate = totalPlanned > 0 ? Math.round((totalCompleted / totalPlanned) * 100) : 0

  // 水果类型统计
  const fruitTypeStats = availableFruits
    .map((fruit) => {
      const count = Object.values(weekData).reduce((sum, day) => {
        return sum + day.filter((f) => f.fruitId === fruit.id && f.completed).length
      }, 0)
      return {
        name: fruit.name,
        emoji: fruit.emoji,
        count,
        color: fruit.color,
      }
    })
    .filter((item) => item.count > 0)

  // 每日完成情况
  const dailyStats = Object.entries(weekData).map(([day, fruits]) => ({
    day: day.split("-")[2] + "日",
    completed: fruits.filter((f) => f.completed).length,
    total: fruits.length,
    rate: fruits.length > 0 ? Math.round((fruits.filter((f) => f.completed).length / fruits.length) * 100) : 0,
  }))

  // 营养统计
  const nutritionStats = Object.values(weekData).reduce(
    (acc, day) => {
      day
        .filter((f) => f.completed)
        .forEach((dayFruit) => {
          const fruit = availableFruits.find((f) => f.id === dayFruit.fruitId)
          if (fruit) {
            acc.calories += fruit.nutrition.calories
            acc.fiber += fruit.nutrition.fiber
          }
        })
      return acc
    },
    { calories: 0, fiber: 0 },
  )

  const COLORS = ["#ff6b6b", "#4ecdc4", "#45b7d1", "#96ceb4", "#feca57", "#ff9ff3", "#54a0ff"]

  return (
    <div className="space-y-6">
      {/* 总体统计 */}
      <div className="grid grid-cols-2 gap-4">
        <Card className="bg-gradient-to-br from-green-50 to-emerald-50 border-green-200">
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">{completionRate}%</div>
            <div className="text-sm text-green-700">本周完成率</div>
            <div className="text-xs text-gray-600 mt-1">
              {totalCompleted}/{totalPlanned}
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-orange-50 to-red-50 border-orange-200">
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-orange-600">{nutritionStats.calories}</div>
            <div className="text-sm text-orange-700">总卡路里</div>
            <div className="text-xs text-gray-600 mt-1">{nutritionStats.fiber}g 纤维</div>
          </CardContent>
        </Card>
      </div>

      {/* 水果类型分布 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center text-lg">
            <TrendingUp className="h-5 w-5 mr-2 text-blue-500" />
            水果类型分布
          </CardTitle>
        </CardHeader>
        <CardContent>
          {fruitTypeStats.length > 0 ? (
            <>
              <div className="h-48 mb-4">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={fruitTypeStats}
                      cx="50%"
                      cy="50%"
                      innerRadius={40}
                      outerRadius={80}
                      paddingAngle={5}
                      dataKey="count"
                    >
                      {fruitTypeStats.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value: number, name: string) => [`${value}次`, name]} />
                  </PieChart>
                </ResponsiveContainer>
              </div>
              <div className="grid grid-cols-2 gap-2">
                {fruitTypeStats.map((fruit, index) => (
                  <div key={fruit.name} className="flex items-center space-x-2">
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: COLORS[index % COLORS.length] }}
                    ></div>
                    <span className="text-sm">
                      {fruit.emoji} {fruit.name}
                    </span>
                    <Badge variant="secondary" className="text-xs">
                      {fruit.count}
                    </Badge>
                  </div>
                ))}
              </div>
            </>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <div className="text-4xl mb-2">🍎</div>
              <p className="text-sm">还没有完成任何水果打卡</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 每日完成情况 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center text-lg">
            <Target className="h-5 w-5 mr-2 text-purple-500" />
            每日完成情况
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-48">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={dailyStats}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="day" tick={{ fontSize: 12 }} />
                <YAxis tick={{ fontSize: 12 }} />
                <Tooltip
                  formatter={(value: number, name: string) => [
                    name === "completed" ? `${value}个` : `${value}%`,
                    name === "completed" ? "已完成" : "完成率",
                  ]}
                />
                <Bar dataKey="completed" fill="#4ecdc4" radius={[4, 4, 0, 0]} />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      {/* 成就徽章 */}
      <Card className="bg-gradient-to-br from-yellow-50 to-orange-50 border-yellow-200">
        <CardHeader>
          <CardTitle className="flex items-center text-lg">
            <Award className="h-5 w-5 mr-2 text-yellow-600" />
            本周成就
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-3">
            {completionRate >= 80 && (
              <Badge className="bg-gradient-to-r from-yellow-400 to-orange-400 text-white p-2 justify-center">
                🏆 完成达人
              </Badge>
            )}
            {fruitTypeStats.length >= 5 && (
              <Badge className="bg-gradient-to-r from-green-400 to-blue-400 text-white p-2 justify-center">
                🌈 多样化饮食
              </Badge>
            )}
            {nutritionStats.calories >= 500 && (
              <Badge className="bg-gradient-to-r from-red-400 to-pink-400 text-white p-2 justify-center">
                ⚡ 能量满满
              </Badge>
            )}
            {totalCompleted >= 10 && (
              <Badge className="bg-gradient-to-r from-purple-400 to-indigo-400 text-white p-2 justify-center">
                💪 坚持不懈
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
