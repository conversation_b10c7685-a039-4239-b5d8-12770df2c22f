"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { Card, CardContent } from "@/components/ui/card"
import { Edit2, Trash2, Save, X } from "lucide-react"

interface Improvement {
  id: string
  title: string
  completed: boolean
}

interface ImprovementItemProps {
  improvement: Improvement
  onUpdate: (improvement: Improvement) => void
  onDelete: (id: string) => void
}

export function ImprovementItem({ improvement, onUpdate, onDelete }: ImprovementItemProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editTitle, setEditTitle] = useState(improvement.title)

  const handleSave = () => {
    onUpdate({
      ...improvement,
      title: editTitle,
    })
    setIsEditing(false)
  }

  const handleCancel = () => {
    setEditTitle(improvement.title)
    setIsEditing(false)
  }

  return (
    <Card
      className={`mb-2 transition-all duration-200 ${improvement.completed ? "opacity-75 bg-gray-50" : "hover:shadow-sm"}`}
    >
      <CardContent className="p-3">
        <div className="flex items-center space-x-3">
          <Checkbox
            checked={improvement.completed}
            onCheckedChange={(checked) => onUpdate({ ...improvement, completed: !!checked })}
          />

          <div className="flex-1">
            {isEditing ? (
              <Input
                value={editTitle}
                onChange={(e) => setEditTitle(e.target.value)}
                placeholder="改进点"
                className="text-sm"
              />
            ) : (
              <span className={`text-sm ${improvement.completed ? "line-through text-gray-500" : "text-gray-900"}`}>
                {improvement.title}
              </span>
            )}
          </div>

          <div className="flex items-center space-x-1">
            {isEditing ? (
              <>
                <Button size="sm" variant="ghost" onClick={handleSave}>
                  <Save className="h-3 w-3" />
                </Button>
                <Button size="sm" variant="ghost" onClick={handleCancel}>
                  <X className="h-3 w-3" />
                </Button>
              </>
            ) : (
              <>
                <Button size="sm" variant="ghost" onClick={() => setIsEditing(true)}>
                  <Edit2 className="h-3 w-3" />
                </Button>
                <Button size="sm" variant="ghost" onClick={() => onDelete(improvement.id)}>
                  <Trash2 className="h-3 w-3" />
                </Button>
              </>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
